const API_ENDPOINT = "http://localhost:8686/v1/chat/completions";

async function testRequest(name, headers) {
    console.log(`\n--- Test: ${name} ---`);
    try {
        const response = await fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                ...headers
            },
            body: JSON.stringify({
                model: "gemini-2.5-flash",
                messages: [{ role: "user", content: "Hello" }]
            })
        });

        const text = await response.text();
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${text}`);
    } catch (error) {
        console.error(`Error during fetch: ${error.message}`);
    }
}

async function runTests() {
    // Test 1: No Authorization header
    await testRequest("No Authorization Header", {});

    // Test 2: Invalid Authorization header
    await testRequest("Invalid Authorization Header", {
        "Authorization": "Bearer invalid-api-key"
    });

    // Test 3: Valid Authorization header
    await testRequest("Valid Authorization Header", {
        "Authorization": "Bearer AIzaSyC_BcZ5WaRpMF-FwQS7a97rMx4etKfIWiw"
    });
}

runTests();